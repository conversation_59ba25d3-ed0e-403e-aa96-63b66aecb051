import { lazy } from 'react'
import { MENUS_PREFIX_PATH } from '@/constants/route.constant'
import { ADMIN, USER } from '@/constants/roles.constant'
import type { Routes } from '@/@types/routes'

const menusRoute: Routes = [
    {
        key: 'menus.unit-structure',
        path: `${MENUS_PREFIX_PATH}/unit-structure`,
        component: lazy(() => import('@/views/menus/UnitStructure')),
        authority: [ADMIN, USER],
        meta: {
            pageContainerType: 'contained',
        },
    },
    {
        key: 'menus.assets',
        path: `${MENUS_PREFIX_PATH}/assets`,
        component: lazy(() => import('@/views/menus/Assets')),
        authority: [ADMIN, USER],
        meta: {
            pageContainerType: 'contained',
        },
    },
    {
        key: 'menus.consumables',
        path: `${MENUS_PREFIX_PATH}/consumables`,
        component: lazy(() => import('@/views/menus/Consumables')),
        authority: [ADMIN, USER],
        meta: {
            pageContainerType: 'contained',
        },
    },
    {
        key: 'menus.warehouses',
        path: `${MENUS_PREFIX_PATH}/warehouses`,
        component: lazy(() => import('@/views/menus/Warehouses')),
        authority: [ADMIN, USER],
        meta: {
            pageContainerType: 'contained',
        },
    },
    {
        key: 'menus.buildings',
        path: `${MENUS_PREFIX_PATH}/buildings`,
        component: lazy(() => import('@/views/menus/Buildings')),
        authority: [ADMIN, USER],
        meta: {
            pageContainerType: 'contained',
        },
    },
    {
        key: 'menus.document-types',
        path: `${MENUS_PREFIX_PATH}/document-types`,
        component: lazy(() => import('@/views/menus/DocumentTypes')),
        authority: [ADMIN, USER],
        meta: {
            pageContainerType: 'contained',
        },
    },
    {
        key: 'menus.categories',
        path: `${MENUS_PREFIX_PATH}/categories`,
        component: lazy(() => import('@/views/menus/Categories')),
        authority: [ADMIN, USER],
        meta: {
            pageContainerType: 'contained',
        },
    },
    {
        key: 'menus.keywords',
        path: `${MENUS_PREFIX_PATH}/keywords`,
        component: lazy(() => import('@/views/menus/Keywords')),
        authority: [ADMIN, USER],
        meta: {
            pageContainerType: 'contained',
        },
    },
    {
        key: 'menus.languages',
        path: `${MENUS_PREFIX_PATH}/languages`,
        component: lazy(() => import('@/views/menus/Languages')),
        authority: [ADMIN, USER],
        meta: {
            pageContainerType: 'contained',
        },
    },
    {
        key: 'menus.entities',
        path: `${MENUS_PREFIX_PATH}/entities`,
        component: lazy(() => import('@/views/menus/Entities')),
        authority: [ADMIN, USER],
        meta: {
            pageContainerType: 'contained',
        },
    },
    {
        key: 'menus.sender-recipient',
        path: `${MENUS_PREFIX_PATH}/sender-recipient`,
        component: lazy(() => import('@/views/menus/SenderRecipient')),
        authority: [ADMIN, USER],
        meta: {
            pageContainerType: 'contained',
        },
    },
]

export default menusRoute
