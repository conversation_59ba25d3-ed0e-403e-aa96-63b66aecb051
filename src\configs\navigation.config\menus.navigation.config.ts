import { MENUS_PREFIX_PATH } from '@/constants/route.constant'
import {
    NAV_ITEM_TYPE_ITEM,
    NAV_ITEM_TYPE_COLLAPSE,
} from '@/constants/navigation.constant'
import { ADMIN, USER } from '@/constants/roles.constant'
import type { NavigationTree } from '@/@types/navigation'

const menusNavigationConfig: NavigationTree[] = [
    {
        key: 'menus',
        path: '',
        title: 'Menus',
        translateKey: 'nav.menus.menus',
        icon: 'menus',
        type: NAV_ITEM_TYPE_COLLAPSE,
        authority: [ADMIN, USER],
        meta: {
            horizontalMenu: {
                layout: 'default',
            },
        },
        subMenu: [
            {
                key: 'menus.unit-structure',
                path: `${MENUS_PREFIX_PATH}/unit-structure`,
                title: 'Unit Structure',
                translateKey: 'nav.menus.unitStructure',
                icon: 'menuUnitStructure',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN, USER],
                subMenu: [],
            },
            {
                key: 'menus.assets',
                path: `${MENUS_PREFIX_PATH}/assets`,
                title: 'Assets',
                translateKey: 'nav.menus.assets',
                icon: 'menuAssets',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN, USER],
                subMenu: [],
            },
            {
                key: 'menus.consumables',
                path: `${MENUS_PREFIX_PATH}/consumables`,
                title: 'Consumables',
                translateKey: 'nav.menus.consumables',
                icon: 'menuConsumables',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN, USER],
                subMenu: [],
            },
            {
                key: 'menus.warehouses',
                path: `${MENUS_PREFIX_PATH}/warehouses`,
                title: 'Warehouses',
                translateKey: 'nav.menus.warehouses',
                icon: 'menuWarehouses',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN, USER],
                subMenu: [],
            },
            {
                key: 'menus.buildings',
                path: `${MENUS_PREFIX_PATH}/buildings`,
                title: 'Buildings',
                translateKey: 'nav.menus.buildings',
                icon: 'menuBuildings',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN, USER],
                subMenu: [],
            },
            {
                key: 'menus.document-types',
                path: `${MENUS_PREFIX_PATH}/document-types`,
                title: 'Document Types',
                translateKey: 'nav.menus.documentTypes',
                icon: 'menuDocumentTypes',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN, USER],
                subMenu: [],
            },
            {
                key: 'menus.categories',
                path: `${MENUS_PREFIX_PATH}/categories`,
                title: 'Categories',
                translateKey: 'nav.menus.categories',
                icon: 'menuCategories',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN, USER],
                subMenu: [],
            },
            {
                key: 'menus.keywords',
                path: `${MENUS_PREFIX_PATH}/keywords`,
                title: 'Keywords',
                translateKey: 'nav.menus.keywords',
                icon: 'menuKeywords',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN, USER],
                subMenu: [],
            },
            {
                key: 'menus.languages',
                path: `${MENUS_PREFIX_PATH}/languages`,
                title: 'Languages',
                translateKey: 'nav.menus.languages',
                icon: 'menuLanguages',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN, USER],
                subMenu: [],
            },
            {
                key: 'menus.entities',
                path: `${MENUS_PREFIX_PATH}/entities`,
                title: 'Entities',
                translateKey: 'nav.menus.entities',
                icon: 'menuEntities',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN, USER],
                subMenu: [],
            },
            {
                key: 'menus.sender-recipient',
                path: `${MENUS_PREFIX_PATH}/sender-recipient`,
                title: 'Sender Recipient',
                translateKey: 'nav.menus.senderRecipient',
                icon: 'menuSender&Recipient',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN, USER],
                subMenu: [],
            },
        ],
    },
]

export default menusNavigationConfig
