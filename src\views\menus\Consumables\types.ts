export type Consumables = {
    id: string
    category: string
    count: number
    description: string
    wareHouse: string
    status: string
    attachments: string
    assignedBy: string
    createdAt: string
}

export type Filter = {
    minAmount: number | string
    maxAmount: number | string
    consumableStatus: string
    consumableType: string[]
}

export type GetConsumableListResponse = {
    list: Consumables[]
    total: number
}
