import Button from '@/components/ui/Button'
import { TbCloudDownload, TbPlus } from 'react-icons/tb'
import { useNavigate } from 'react-router-dom'
import useConsumableList from '../hooks/useConsumableList'
import { CSVLink } from 'react-csv'

const ConsumableListActionTools = () => {
    const navigate = useNavigate()

    const { consumableList } = useConsumableList()

    return (
        <div className="flex flex-col md:flex-row gap-3">
            <CSVLink filename="product-list.csv" data={consumableList}>
                <Button icon={<TbCloudDownload className="text-xl" />}>
                    Export
                </Button>
            </CSVLink>
            <Button
                variant="solid"
                icon={<TbPlus className="text-xl" />}
                onClick={() => navigate('/concepts/products/product-create')}
            >
                Add Consumables
            </Button>
        </div>
    )
}

export default ConsumableListActionTools
